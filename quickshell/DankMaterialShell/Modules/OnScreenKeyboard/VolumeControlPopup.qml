import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import QtQuick.Effects
import Quickshell
import Quickshell.Wayland
import Quickshell.Widgets
import qs.Common
import qs.Services
import qs.Widgets

/**
 * 🇺🇸 VOLUME CONTROL POPUP - AMERICA DEMANDS CONVENIENT AUDIO ACCESS!
 * 
 * A dedicated popup for volume and media controls that appears when needed,
 * keeping the main keyboard interface clean and focused.
 */
PanelWindow {
    id: volumePopup
    
    property bool volumeVisible: false
    property real triggerX: 50
    property real triggerY: 100
    property string triggerSection: "left"
    
    // Window properties
    anchors {
        left: true
        top: true
    }
    
    width: 80
    height: Math.min(400, Screen.height - 100)
    
    visible: volumeVisible
    layer: Layer.Overlay
    keyboardFocus: Layer.None
    
    // Position calculation
    x: triggerX
    y: Math.max(50, Math.min(triggerY, Screen.height - height - 50))
    
    // Animation properties
    opacity: volumeVisible ? 1 : 0
    scale: volumeVisible ? 1 : 0.9
    
    Behavior on opacity {
        NumberAnimation {
            duration: 200
            easing.type: Easing.OutCubic
        }
    }
    
    Behavior on scale {
        NumberAnimation {
            duration: 200
            easing.type: Easing.OutCubic
        }
    }
    
    // Auto-hide timer
    Timer {
        id: autoHideTimer
        interval: 5000 // Hide after 5 seconds of inactivity
        onTriggered: volumePopup.volumeVisible = false
    }
    
    function show() {
        volumeVisible = true
        autoHideTimer.restart()
    }
    
    function hide() {
        volumeVisible = false
        autoHideTimer.stop()
    }
    
    function resetAutoHide() {
        if (volumeVisible) {
            autoHideTimer.restart()
        }
    }
    
    // Main container
    StyledRect {
        anchors.fill: parent
        color: Theme.popupBackground()
        radius: Theme.cornerRadius
        border.width: 1
        border.color: Theme.outlineMedium
        
        // Drop shadow effect
        layer.enabled: true
        layer.effect: DropShadow {
            radius: 16
            samples: 33
            color: Qt.rgba(0, 0, 0, 0.2)
            horizontalOffset: 0
            verticalOffset: 4
        }
        
        ColumnLayout {
            anchors.fill: parent
            anchors.margins: Theme.spacingM
            spacing: Theme.spacingS
            
            // Title
            StyledText {
                text: "Audio"
                font.pixelSize: 12
                font.bold: true
                color: Theme.surfaceVariantText
                Layout.alignment: Qt.AlignHCenter
            }
            
            Rectangle {
                Layout.fillWidth: true
                Layout.preferredHeight: 1
                color: Theme.outlineMedium
            }
            
            // Volume Controls
            ColumnLayout {
                spacing: Theme.spacingXS
                Layout.fillWidth: true
                
                // Mute toggle
                DankActionButton {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 40
                    backgroundColor: AudioService.sink && AudioService.sink.audio && AudioService.sink.audio.muted 
                                    ? Theme.primary : "transparent"
                    
                    onClicked: {
                        AudioService.toggleMute()
                        volumePopup.resetAutoHide()
                    }
                    
                    RowLayout {
                        anchors.centerIn: parent
                        spacing: Theme.spacingXS
                        
                        DankIcon {
                            name: AudioService.sink && AudioService.sink.audio && AudioService.sink.audio.muted 
                                  ? "volume_off" : "volume_up"
                            size: 16
                        }
                        
                        StyledText {
                            text: "Mute"
                            font.pixelSize: 10
                            color: parent.parent.backgroundColor == Theme.primary ? Theme.primaryText : Theme.surfaceVariantText
                        }
                    }
                }
                
                // Volume up
                DankActionButton {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 35
                    
                    onClicked: {
                        if (AudioService.sink && AudioService.sink.audio) {
                            const currentVolume = Math.round(AudioService.sink.audio.volume * 100)
                            const newVolume = Math.min(100, currentVolume + 10)
                            AudioService.sink.audio.volume = newVolume / 100
                            if (AudioService.sink.audio.muted) AudioService.sink.audio.muted = false
                        }
                        volumePopup.resetAutoHide()
                    }
                    
                    RowLayout {
                        anchors.centerIn: parent
                        spacing: Theme.spacingXS
                        
                        DankIcon {
                            name: "volume_up"
                            size: 14
                        }
                        
                        StyledText {
                            text: "+10%"
                            font.pixelSize: 9
                            color: Theme.surfaceVariantText
                        }
                    }
                }
                
                // Volume down
                DankActionButton {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 35
                    
                    onClicked: {
                        if (AudioService.sink && AudioService.sink.audio) {
                            const currentVolume = Math.round(AudioService.sink.audio.volume * 100)
                            const newVolume = Math.max(0, currentVolume - 10)
                            AudioService.sink.audio.volume = newVolume / 100
                            if (AudioService.sink.audio.muted) AudioService.sink.audio.muted = false
                        }
                        volumePopup.resetAutoHide()
                    }
                    
                    RowLayout {
                        anchors.centerIn: parent
                        spacing: Theme.spacingXS
                        
                        DankIcon {
                            name: "volume_down"
                            size: 14
                        }
                        
                        StyledText {
                            text: "-10%"
                            font.pixelSize: 9
                            color: Theme.surfaceVariantText
                        }
                    }
                }
                
                // Volume indicator
                StyledText {
                    text: AudioService.sink && AudioService.sink.audio 
                          ? Math.round(AudioService.sink.audio.volume * 100) + "%" 
                          : "N/A"
                    font.pixelSize: 11
                    color: Theme.surfaceVariantText
                    Layout.alignment: Qt.AlignHCenter
                }
            }
            
            Rectangle {
                Layout.fillWidth: true
                Layout.preferredHeight: 1
                color: Theme.outlineMedium
            }
            
            // Media Controls
            ColumnLayout {
                spacing: Theme.spacingXS
                Layout.fillWidth: true
                
                StyledText {
                    text: "Media"
                    font.pixelSize: 10
                    font.bold: true
                    color: Theme.surfaceVariantText
                    Layout.alignment: Qt.AlignHCenter
                }
                
                // Play/Pause
                DankActionButton {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 35
                    enabled: MprisController.activePlayer?.canTogglePlaying ?? false
                    backgroundColor: MprisController.activePlayer?.isPlaying ? Theme.primary : "transparent"
                    
                    onClicked: {
                        MprisController.activePlayer?.togglePlaying()
                        volumePopup.resetAutoHide()
                    }
                    
                    DankIcon {
                        anchors.centerIn: parent
                        name: MprisController.activePlayer?.isPlaying ? "pause" : "play_arrow"
                        size: 16
                    }
                }
                
                // Previous
                DankActionButton {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 30
                    enabled: MprisController.activePlayer?.canGoPrevious ?? false
                    
                    onClicked: {
                        MprisController.activePlayer?.previous()
                        volumePopup.resetAutoHide()
                    }
                    
                    DankIcon {
                        anchors.centerIn: parent
                        name: "skip_previous"
                        size: 14
                    }
                }
                
                // Next
                DankActionButton {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 30
                    enabled: MprisController.activePlayer?.canGoNext ?? false
                    
                    onClicked: {
                        MprisController.activePlayer?.next()
                        volumePopup.resetAutoHide()
                    }
                    
                    DankIcon {
                        anchors.centerIn: parent
                        name: "skip_next"
                        size: 14
                    }
                }
            }
            
            Item {
                Layout.fillHeight: true
            }
        }
    }
    
    // Click outside to close
    MouseArea {
        anchors.fill: parent
        z: -1
        onClicked: volumePopup.hide()
    }
}

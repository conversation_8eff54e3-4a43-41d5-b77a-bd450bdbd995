import QtQuick
import QtQuick.Layouts
import QtQuick.Controls
import "../../Common"
import "../../Services"
import "../../Widgets"
import "./layouts.js" as Layouts

Item {
    id: root

    property var layouts: Layouts.byName
    property var activeLayoutName: "English (US)"
    property var currentLayout: layouts[activeLayoutName]

    // 🚨 AMERICA NEEDS NUMPAD TOGGLE!
    property bool showNumpad: false

    function toggleNumpad() {
        showNumpad = !showNumpad
    }

    // 🚨 AMERICA'S EMERGENCY SCALING SYSTEM - SEPARATE WIDTH/HEIGHT SCALING!
    property real availableWidth: width - 40
    property real availableHeight: height - 80
    property real baseKeyboardWidth: 800
    property real baseKeyboardHeight: 300

    // 🇺🇸 SEPARATE SCALING FOR WIDTH AND HEIGHT - AMERICA DEMANDS IT!
    property real widthScale: Math.max(0.4, availableWidth / baseKeyboardWidth)
    property real heightScale: Math.max(0.4, availableHeight / baseKeyboardHeight)

    implicitWidth: keyRows.implicitWidth
    implicitHeight: keyRows.implicitHeight

    RowLayout {
        id: mainLayout
        anchors.fill: parent
        anchors.margins: Math.max(5, 10 * Math.min(root.widthScale, root.heightScale))
        spacing: Math.max(5, 10 * root.widthScale)

        // 🇺🇸 MAIN KEYBOARD SECTION
        ColumnLayout {
            id: keyRows
            Layout.fillWidth: true
            Layout.fillHeight: true
            spacing: Math.max(2, 5 * root.heightScale)

            Repeater {
                model: root.activeLayoutName === "English (US)" ? root.currentLayout.keys : []

                delegate: RowLayout {
                    id: keyRow
                    required property var modelData
                    spacing: Math.max(2, 5 * root.widthScale)
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    Layout.preferredHeight: Math.max(40, 50 * root.heightScale)
                    Layout.minimumHeight: Math.max(30, 40 * root.heightScale)
                    Layout.maximumHeight: Math.max(60, 80 * root.heightScale)

                    Repeater {
                        model: modelData
                        delegate: OskKey {
                            required property var modelData
                            keyData: modelData

                            // 🚨 EMERGENCY SCALING - WIDTH AND HEIGHT SCALE INDEPENDENTLY!
                            baseWidth: Math.max(30, 45 * root.widthScale)
                            baseHeight: Math.max(10, 40 * root.heightScale)
                        }
                    }
                }
            }
        }

        // 🚨 NUMPAD ON THE RIGHT SIDE - AMERICA DEMANDS IT!
        ColumnLayout {
            id: numpadRows
            Layout.preferredWidth: Math.max(200, 250 * root.widthScale)
            Layout.fillHeight: true
            spacing: Math.max(2, 5 * root.heightScale)
            visible: root.showNumpad

            Repeater {
                model: root.layouts["Numpad"] ? root.layouts["Numpad"].keys : []

                delegate: RowLayout {
                    id: numpadRow
                    required property var modelData
                    spacing: Math.max(2, 4 * root.widthScale)
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    Layout.preferredHeight: Math.max(35, 45 * root.heightScale)

                    Repeater {
                        model: modelData
                        delegate: OskKey {
                            required property var modelData
                            keyData: modelData

                            baseWidth: Math.max(25, 35 * root.widthScale)
                            baseHeight: Math.max(25, 40 * root.heightScale)
                        }
                    }
                }
            }
        }
    }
}

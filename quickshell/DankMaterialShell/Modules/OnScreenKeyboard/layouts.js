// We're going to use ydotool
// See /usr/include/linux/input-event-codes.h for keycodes

const defaultLayout = "English (US)";
const byName = {
    "VolumeControls": {
        name_short: "VOL",
        description: "Volume Controls",
        comment: "Audio control keys for America",
        keys: [
            [
                { keytype: "normal", label: "Mute", shape: "normal", keycode: 113 }
            ],
            [
                { keytype: "normal", label: "Vol+", shape: "normal", keycode: 115 }
            ],
            [
                { keytype: "normal", label: "Vol-", shape: "normal", keycode: 114 }
            ],
            [
                { keytype: "normal", label: "Play", shape: "normal", keycode: 164 }
            ],
            [
                { keytype: "normal", label: "Prev", shape: "normal", keycode: 165 }
            ],
            [
                { keytype: "normal", label: "Next", shape: "normal", keycode: 163 }
            ]
        ]
    },
    "Numpad": {
        name_short: "123",
        description: "Number Pad",
        comment: "Numeric keypad for America",
        keys: [
            [
                { keytype: "normal", label: "Num", shape: "normal", keycode: 69 },
                { keytype: "normal", label: "/", shape: "normal", keycode: 98 },
                { keytype: "normal", label: "*", shape: "normal", keycode: 55 },
                { keytype: "normal", label: "-", shape: "normal", keycode: 74 }
            ],
            [
                { keytype: "normal", label: "7", shape: "normal", keycode: 71 },
                { keytype: "normal", label: "8", shape: "normal", keycode: 72 },
                { keytype: "normal", label: "9", shape: "normal", keycode: 73 },
                { keytype: "normal", label: "+", shape: "expand", keycode: 78 }
            ],
            [
                { keytype: "normal", label: "4", shape: "normal", keycode: 75 },
                { keytype: "normal", label: "5", shape: "normal", keycode: 76 },
                { keytype: "normal", label: "6", shape: "normal", keycode: 77 },
                { keytype: "empty", label: "", shape: "empty", keycode: 0 }
            ],
            [
                { keytype: "normal", label: "1", shape: "normal", keycode: 79 },
                { keytype: "normal", label: "2", shape: "normal", keycode: 80 },
                { keytype: "normal", label: "3", shape: "normal", keycode: 81 },
                { keytype: "normal", label: "Enter", shape: "expand", keycode: 96 }
            ],
            [
                { keytype: "normal", label: "0", shape: "space", keycode: 82 },
                { keytype: "normal", label: ".", shape: "normal", keycode: 83 },
                { keytype: "empty", label: "", shape: "empty", keycode: 0 }
            ]
        ]
    },
    "English (US)": {
        name_short: "US",
        description: "QWERTY - Full",
        comment: "Like physical keyboard",
        keys: [
            [
                { keytype: "normal", label: "Esc", shape: "fn", keycode: 1 },
                { keytype: "normal", label: "F1", shape: "fn", keycode: 59 },
                { keytype: "normal", label: "F2", shape: "fn", keycode: 60 },
                { keytype: "normal", label: "F3", shape: "fn", keycode: 61 },
                { keytype: "normal", label: "F4", shape: "fn", keycode: 62 },
                { keytype: "normal", label: "F5", shape: "fn", keycode: 63 },
                { keytype: "normal", label: "F6", shape: "fn", keycode: 64 },
                { keytype: "normal", label: "F7", shape: "fn", keycode: 65 },
                { keytype: "normal", label: "F8", shape: "fn", keycode: 66 },
                { keytype: "normal", label: "F9", shape: "fn", keycode: 67 },
                { keytype: "normal", label: "F10", shape: "fn", keycode: 68 },
                { keytype: "normal", label: "F11", shape: "fn", keycode: 87 },
                { keytype: "normal", label: "F12", shape: "fn", keycode: 88 },
                { keytype: "normal", label: "PrtSc", shape: "fn", keycode: 99 },
                { keytype: "normal", label: "ScrLk", shape: "fn", keycode: 70 },
                { keytype: "normal", label: "Pause", shape: "fn", keycode: 119 }
            ],
            [
                { keytype: "normal", label: "`", labelShift: "~", shape: "normal", keycode: 41 },
                { keytype: "normal", label: "1", labelShift: "!", shape: "normal", keycode: 2 },
                { keytype: "normal", label: "2", labelShift: "@", shape: "normal", keycode: 3 },
                { keytype: "normal", label: "3", labelShift: "#", shape: "normal", keycode: 4 },
                { keytype: "normal", label: "4", labelShift: "$", shape: "normal", keycode: 5 },
                { keytype: "normal", label: "5", labelShift: "%", shape: "normal", keycode: 6 },
                { keytype: "normal", label: "6", labelShift: "^", shape: "normal", keycode: 7 },
                { keytype: "normal", label: "7", labelShift: "&", shape: "normal", keycode: 8 },
                { keytype: "normal", label: "8", labelShift: "*", shape: "normal", keycode: 9 },
                { keytype: "normal", label: "9", labelShift: "(", shape: "normal", keycode: 10 },
                { keytype: "normal", label: "0", labelShift: ")", shape: "normal", keycode: 11 },
                { keytype: "normal", label: "-", labelShift: "_", shape: "normal", keycode: 12 },
                { keytype: "normal", label: "=", labelShift: "+", shape: "normal", keycode: 13 },
                { keytype: "normal", label: "Backspace", shape: "expand", keycode: 14 },
                { keytype: "normal", label: "Insert", shape: "fn", keycode: 110 },
                { keytype: "normal", label: "Home", shape: "fn", keycode: 102 },
                { keytype: "normal", label: "PgUp", shape: "fn", keycode: 104 }
            ],
            [
                { keytype: "normal", label: "Tab", shape: "tab", keycode: 15 },
                { keytype: "normal", label: "q", labelShift: "Q", shape: "normal", keycode: 16 },
                { keytype: "normal", label: "w", labelShift: "W", shape: "normal", keycode: 17 },
                { keytype: "normal", label: "e", labelShift: "E", shape: "normal", keycode: 18 },
                { keytype: "normal", label: "r", labelShift: "R", shape: "normal", keycode: 19 },
                { keytype: "normal", label: "t", labelShift: "T", shape: "normal", keycode: 20 },
                { keytype: "normal", label: "y", labelShift: "Y", shape: "normal", keycode: 21 },
                { keytype: "normal", label: "u", labelShift: "U", shape: "normal", keycode: 22 },
                { keytype: "normal", label: "i", labelShift: "I", shape: "normal", keycode: 23 },
                { keytype: "normal", label: "o", labelShift: "O", shape: "normal", keycode: 24 },
                { keytype: "normal", label: "p", labelShift: "P", shape: "normal", keycode: 25 },
                { keytype: "normal", label: "[", labelShift: "{", shape: "normal", keycode: 26 },
                { keytype: "normal", label: "]", labelShift: "}", shape: "normal", keycode: 27 },
                { keytype: "normal", label: "\\", labelShift: "|", shape: "expand", keycode: 43 }
            ],
            [
                { keytype: "toggle", label: "Caps", shape: "caps", keycode: 58 },
                { keytype: "normal", label: "a", labelShift: "A", shape: "normal", keycode: 30 },
                { keytype: "normal", label: "s", labelShift: "S", shape: "normal", keycode: 31 },
                { keytype: "normal", label: "d", labelShift: "D", shape: "normal", keycode: 32 },
                { keytype: "normal", label: "f", labelShift: "F", shape: "normal", keycode: 33 },
                { keytype: "normal", label: "g", labelShift: "G", shape: "normal", keycode: 34 },
                { keytype: "normal", label: "h", labelShift: "H", shape: "normal", keycode: 35 },
                { keytype: "normal", label: "j", labelShift: "J", shape: "normal", keycode: 36 },
                { keytype: "normal", label: "k", labelShift: "K", shape: "normal", keycode: 37 },
                { keytype: "normal", label: "l", labelShift: "L", shape: "normal", keycode: 38 },
                { keytype: "normal", label: ";", labelShift: ":", shape: "normal", keycode: 39 },
                { keytype: "normal", label: "'", labelShift: "\"", shape: "normal", keycode: 40 },
                { keytype: "normal", label: "Enter", shape: "expand", keycode: 28 }
            ],
            [
                { keytype: "modkey", label: "Shift", labelShift: "Shift", labelCaps: "Caps", shape: "shift", keycode: 42 },
                { keytype: "normal", label: "z", labelShift: "Z", shape: "normal", keycode: 44 },
                { keytype: "normal", label: "x", labelShift: "X", shape: "normal", keycode: 45 },
                { keytype: "normal", label: "c", labelShift: "C", shape: "normal", keycode: 46 },
                { keytype: "normal", label: "v", labelShift: "V", shape: "normal", keycode: 47 },
                { keytype: "normal", label: "b", labelShift: "B", shape: "normal", keycode: 48 },
                { keytype: "normal", label: "n", labelShift: "N", shape: "normal", keycode: 49 },
                { keytype: "normal", label: "m", labelShift: "M", shape: "normal", keycode: 50 },
                { keytype: "normal", label: ",", labelShift: "<", shape: "normal", keycode: 51 },
                { keytype: "normal", label: ".", labelShift: ">", shape: "normal", keycode: 52 },
                { keytype: "normal", label: "/", labelShift: "?", shape: "normal", keycode: 53 },
                { keytype: "modkey", label: "Shift", labelShift: "Shift", labelCaps: "Caps", shape: "expand", keycode: 54 }
            ],
            [
                { keytype: "modkey", label: "Ctrl", shape: "control", keycode: 29 },
                { keytype: "modkey", label: "Win", shape: "normal", keycode: 125 },
                { keytype: "modkey", label: "Alt", shape: "normal", keycode: 56 },
                { keytype: "normal", label: "Space", shape: "space", keycode: 57 },
                { keytype: "modkey", label: "Alt", shape: "normal", keycode: 100 },
                { keytype: "normal", label: "Menu", shape: "normal", keycode: 139 },
                { keytype: "modkey", label: "Ctrl", shape: "control", keycode: 97 }
            ],
            [
                { keytype: "normal", label: "Del", shape: "fn", keycode: 111 },
                { keytype: "normal", label: "End", shape: "fn", keycode: 107 },
                { keytype: "normal", label: "PgDn", shape: "fn", keycode: 109 }
            ]
            [
                { keytype: "empty", label: "", shape: "empty", keycode: 0 },
                { keytype: "empty", label: "", shape: "empty", keycode: 0 },
                { keytype: "empty", label: "", shape: "empty", keycode: 0 },
                { keytype: "empty", label: "", shape: "empty", keycode: 0 },
                { keytype: "empty", label: "", shape: "empty", keycode: 0 },
                { keytype: "empty", label: "", shape: "empty", keycode: 0 },
                { keytype: "empty", label: "", shape: "empty", keycode: 0 },
                { keytype: "empty", label: "", shape: "empty", keycode: 0 },
                { keytype: "empty", label: "", shape: "empty", keycode: 0 },
                { keytype: "empty", label: "", shape: "empty", keycode: 0 },
                { keytype: "empty", label: "", shape: "empty", keycode: 0 },
                { keytype: "normal", label: "↑", shape: "normal", keycode: 103 },
                { keytype: "empty", label: "", shape: "empty", keycode: 0 },
                { keytype: "empty", label: "", shape: "empty", keycode: 0 },
                { keytype: "empty", label: "", shape: "empty", keycode: 0 },
                { keytype: "empty", label: "", shape: "empty", keycode: 0 },
                { keytype: "empty", label: "", shape: "empty", keycode: 0 }
            ],
            [
                { keytype: "empty", label: "", shape: "empty", keycode: 0 },
                { keytype: "empty", label: "", shape: "empty", keycode: 0 },
                { keytype: "empty", label: "", shape: "empty", keycode: 0 },
                { keytype: "empty", label: "", shape: "empty", keycode: 0 },
                { keytype: "empty", label: "", shape: "empty", keycode: 0 },
                { keytype: "empty", label: "", shape: "empty", keycode: 0 },
                { keytype: "empty", label: "", shape: "empty", keycode: 0 },
                { keytype: "empty", label: "", shape: "empty", keycode: 0 },
                { keytype: "empty", label: "", shape: "empty", keycode: 0 },
                { keytype: "empty", label: "", shape: "empty", keycode: 0 },
                { keytype: "normal", label: "←", shape: "normal", keycode: 105 },
                { keytype: "normal", label: "↓", shape: "normal", keycode: 108 },
                { keytype: "normal", label: "→", shape: "normal", keycode: 106 },
                { keytype: "empty", label: "", shape: "empty", keycode: 0 },
                { keytype: "empty", label: "", shape: "empty", keycode: 0 },
                { keytype: "empty", label: "", shape: "empty", keycode: 0 },
                { keytype: "empty", label: "", shape: "empty", keycode: 0 }
            ]
        ]
    }
}

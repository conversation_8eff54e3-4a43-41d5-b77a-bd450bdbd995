import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import Quickshell
import Quickshell.Wayland
import "../../Common"
import "../../Services"
import "../../Widgets"

Item {
    id: root
    property bool pinned: false

    Loader {
        id: oskLoader
        active: SessionData.oskOpen
        
        onActiveChanged: {
            if (!oskLoader.active) {
                YdotoolService.releaseAllKeys();
            }
        }
        
        sourceComponent: PanelWindow {
            id: oskRoot
            visible: oskLoader.active

            property real keyboardWidth: Math.max(600, Math.min(Screen.width * 0.9, oskContent.implicitWidth + 120))
            property real keyboardHeight: Math.max(300, Math.min(Screen.height * 0.6, oskContent.implicitHeight + 120))

            function hide() {
                SessionData.oskOpen = false
            }

            implicitWidth: keyboardWidth
            implicitHeight: keyboardHeight
            WlrLayershell.namespace: "quickshell:osk"
            WlrLayershell.layer: WlrLayer.Overlay
            WlrLayershell.exclusiveZone: root.pinned ? implicitHeight : 0
            color: "transparent"

            anchors {
                bottom: true
                left: true
                right: true
            }

            mask: Region {
                item: oskBackground
            }

                Rectangle {
                    id: oskBackground
                    anchors.centerIn: parent

                    property real padding: 10
                    property real minWidth: 400
                    property real minHeight: 200
                    property real maxWidth: Screen.width * 0.9
                    property real maxHeight: Screen.height * 0.6

                    width: oskRoot.keyboardWidth
                    height: oskRoot.keyboardHeight

                    // Follow theme colors like other components
                    color: Qt.rgba(Theme.surfaceContainer.r, Theme.surfaceContainer.g, Theme.surfaceContainer.b, 0.95)
                    border.color: Theme.outlineMedium
                    border.width: 1
                    radius: Theme.cornerRadius

                    // Add surface tint like dock
                    Rectangle {
                        anchors.fill: parent
                        color: Qt.rgba(Theme.surfaceTint.r, Theme.surfaceTint.g, Theme.surfaceTint.b, 0.04)
                        radius: parent.radius
                    }

                    Keys.onPressed: (event) => {
                        if (event.key === Qt.Key_Escape) {
                            oskRoot.hide()
                        }
                    }



                // Drag handle at the top
                Rectangle {
                    id: dragHandle
                    width: parent.width
                    height: 30
                    color: Qt.rgba(Theme.surfaceVariant.r, Theme.surfaceVariant.g, Theme.surfaceVariant.b, 0.5)
                    radius: parent.radius

                    Rectangle {
                        width: 40
                        height: 4
                        radius: 2
                        color: Theme.outline
                        anchors.centerIn: parent
                    }

                    MouseArea {
                        id: dragArea
                        anchors.fill: parent
                        cursorShape: Qt.SizeAllCursor

                        property point startPos
                        property point startWindowPos

                        onPressed: (mouse) => {
                            startPos = Qt.point(mouse.x, mouse.y)
                        }

                        onPositionChanged: (mouse) => {
                            if (pressed) {
                                var delta = Qt.point(mouse.x - startPos.x, mouse.y - startPos.y)

                                // Provide visual feedback during drag
                                var maxOffset = 8
                                oskBackground.x = Math.max(-maxOffset, Math.min(maxOffset, delta.x * 0.2))
                                oskBackground.y = Math.max(-maxOffset, Math.min(maxOffset, delta.y * 0.2))
                            }
                        }

                        onReleased: {
                            // Reset position after drag
                            oskBackground.x = 0
                            oskBackground.y = 0
                        }
                    }
                }

                // 🚀 FOUR CORNER RESIZE HANDLES - AMERICA DEMANDS IT!

                // Top-left resize handle
                Rectangle {
                    id: resizeHandleTL
                    width: 20
                    height: 20
                    anchors.top: parent.top
                    anchors.left: parent.left
                    color: Theme.primary
                    radius: 4
                    opacity: resizeAreaTL.containsMouse ? 0.8 : 0.5

                    Rectangle {
                        width: 8
                        height: 8
                        anchors.centerIn: parent
                        color: Theme.primaryText
                        radius: 2
                    }

                    MouseArea {
                        id: resizeAreaTL
                        anchors.fill: parent
                        anchors.margins: -5
                        cursorShape: Qt.SizeFDiagCursor
                        hoverEnabled: true

                        property point startPos
                        property size startSize

                        onPressed: (mouse) => {
                            startPos = Qt.point(mouse.x, mouse.y)
                            startSize = Qt.size(oskRoot.keyboardWidth, oskRoot.keyboardHeight)
                        }

                        onPositionChanged: (mouse) => {
                            if (pressed) {
                                var delta = Qt.point(mouse.x - startPos.x, mouse.y - startPos.y)
                                var newWidth = Math.max(500, Math.min(Screen.width * 0.95, startSize.width - delta.x))
                                var newHeight = Math.max(250, Math.min(Screen.height * 0.7, startSize.height - delta.y))

                                oskRoot.keyboardWidth = newWidth
                                oskRoot.keyboardHeight = newHeight
                            }
                        }
                    }
                }

                // Top-right resize handle
                Rectangle {
                    id: resizeHandleTR
                    width: 20
                    height: 20
                    anchors.top: parent.top
                    anchors.right: parent.right
                    color: Theme.primary
                    radius: 4
                    opacity: resizeAreaTR.containsMouse ? 0.8 : 0.5

                    Rectangle {
                        width: 8
                        height: 8
                        anchors.centerIn: parent
                        color: Theme.primaryText
                        radius: 2
                    }

                    MouseArea {
                        id: resizeAreaTR
                        anchors.fill: parent
                        anchors.margins: -5
                        cursorShape: Qt.SizeBDiagCursor
                        hoverEnabled: true

                        property point startPos
                        property size startSize

                        onPressed: (mouse) => {
                            startPos = Qt.point(mouse.x, mouse.y)
                            startSize = Qt.size(oskRoot.keyboardWidth, oskRoot.keyboardHeight)
                        }

                        onPositionChanged: (mouse) => {
                            if (pressed) {
                                var delta = Qt.point(mouse.x - startPos.x, mouse.y - startPos.y)
                                var newWidth = Math.max(500, Math.min(Screen.width * 0.95, startSize.width + delta.x))
                                var newHeight = Math.max(250, Math.min(Screen.height * 0.7, startSize.height - delta.y))

                                oskRoot.keyboardWidth = newWidth
                                oskRoot.keyboardHeight = newHeight
                            }
                        }
                    }
                }

                // Bottom-left resize handle
                Rectangle {
                    id: resizeHandleBL
                    width: 20
                    height: 20
                    anchors.bottom: parent.bottom
                    anchors.left: parent.left
                    color: Theme.primary
                    radius: 4
                    opacity: resizeAreaBL.containsMouse ? 0.8 : 0.5

                    Rectangle {
                        width: 8
                        height: 8
                        anchors.centerIn: parent
                        color: Theme.primaryText
                        radius: 2
                    }

                    MouseArea {
                        id: resizeAreaBL
                        anchors.fill: parent
                        anchors.margins: -5
                        cursorShape: Qt.SizeBDiagCursor
                        hoverEnabled: true

                        property point startPos
                        property size startSize

                        onPressed: (mouse) => {
                            startPos = Qt.point(mouse.x, mouse.y)
                            startSize = Qt.size(oskRoot.keyboardWidth, oskRoot.keyboardHeight)
                        }

                        onPositionChanged: (mouse) => {
                            if (pressed) {
                                var delta = Qt.point(mouse.x - startPos.x, mouse.y - startPos.y)
                                var newWidth = Math.max(500, Math.min(Screen.width * 0.95, startSize.width - delta.x))
                                var newHeight = Math.max(250, Math.min(Screen.height * 0.7, startSize.height + delta.y))

                                oskRoot.keyboardWidth = newWidth
                                oskRoot.keyboardHeight = newHeight
                            }
                        }
                    }
                }

                // Bottom-right resize handle (original)
                Rectangle {
                    id: resizeHandleBR
                    width: 20
                    height: 20
                    anchors.bottom: parent.bottom
                    anchors.right: parent.right
                    color: Theme.primary
                    radius: 4
                    opacity: resizeAreaBR.containsMouse ? 0.8 : 0.5

                    Rectangle {
                        width: 8
                        height: 8
                        anchors.centerIn: parent
                        color: Theme.primaryText
                        radius: 2
                    }

                    MouseArea {
                        id: resizeAreaBR
                        anchors.fill: parent
                        anchors.margins: -5
                        cursorShape: Qt.SizeFDiagCursor
                        hoverEnabled: true

                        property point startPos
                        property size startSize

                        onPressed: (mouse) => {
                            startPos = Qt.point(mouse.x, mouse.y)
                            startSize = Qt.size(oskRoot.keyboardWidth, oskRoot.keyboardHeight)
                        }

                        onPositionChanged: (mouse) => {
                            if (pressed) {
                                var delta = Qt.point(mouse.x - startPos.x, mouse.y - startPos.y)
                                var newWidth = Math.max(500, Math.min(Screen.width * 0.95, startSize.width + delta.x))
                                var newHeight = Math.max(250, Math.min(Screen.height * 0.7, startSize.height + delta.y))

                                oskRoot.keyboardWidth = newWidth
                                oskRoot.keyboardHeight = newHeight
                            }
                        }
                    }
                }

                Behavior on opacity {
                    NumberAnimation { duration: 200 }
                }

                RowLayout {
                    id: oskRowLayout
                    anchors.fill: parent
                    anchors.margins: 15
                    anchors.topMargin: 45 // Account for drag handle
                    anchors.bottomMargin: 45 // Account for resize handle and bottom spacing
                    spacing: 5

                    Column {
                        spacing: 5
                        Layout.alignment: Qt.AlignTop
                        Layout.preferredWidth: 50
                        
                        DankActionButton {
                            width: 40
                            height: 40
                            backgroundColor: root.pinned ? Theme.primary : "transparent"
                            onClicked: root.pinned = !root.pinned
                            
                            DankIcon {
                                anchors.centerIn: parent
                                name: "keep"
                                size: 24
                            }
                        }
                        
                        DankActionButton {
                            width: 40
                            height: 40
                            backgroundColor: oskContent.showVolumeControls ? Theme.primary : "transparent"
                            onClicked: oskContent.toggleVolumeControls()

                            DankIcon {
                                anchors.centerIn: parent
                                name: "volume_up"
                                size: 24
                            }
                        }

                        DankActionButton {
                            width: 40
                            height: 40
                            backgroundColor: oskContent.showNumpad ? Theme.primary : "transparent"
                            onClicked: oskContent.toggleNumpad()

                            DankIcon {
                                anchors.centerIn: parent
                                name: "dialpad"
                                size: 24
                            }
                        }

                        DankActionButton {
                            width: 40
                            height: 40
                            onClicked: oskRoot.hide()

                            DankIcon {
                                anchors.centerIn: parent
                                name: "keyboard_hide"
                                size: 24
                            }
                        }
                    }
                    
                    Rectangle {
                        Layout.topMargin: 20
                        Layout.bottomMargin: 20
                        Layout.fillHeight: true
                        implicitWidth: 1
                        color: Theme.outlineMedium
                    }

                    OskContent {
                        id: oskContent
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        Layout.preferredWidth: 400
                        Layout.preferredHeight: 200
                    }
                }
            }
        }
    }
}
